import os
import time
import psutil
import threading
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

import tensorflow as tf
import numpy as np
from data_loader import UyghurDataGenerator
from model import build_robust_asr_model
from config import *

def monitor_system():
    """监控系统资源使用情况"""
    while True:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        print(f"[监控] CPU: {cpu_percent:.1f}%, 内存: {memory.percent:.1f}%")
        time.sleep(5)

print("===== 调试训练问题 =====")

# 启动系统监控
monitor_thread = threading.Thread(target=monitor_system, daemon=True)
monitor_thread.start()

# 1. 测试基础TensorFlow功能
print("\n===== 测试TensorFlow基础功能 =====")
try:
    print(f"TensorFlow版本: {tf.__version__}")
    print(f"可用设备: {tf.config.list_physical_devices()}")
    
    # 简单的张量运算
    a = tf.constant([1, 2, 3])
    b = tf.constant([4, 5, 6])
    c = tf.add(a, b)
    print(f"简单运算测试: {a} + {b} = {c}")
    print("✅ TensorFlow基础功能正常")
except Exception as e:
    print(f"❌ TensorFlow基础功能异常: {e}")
    exit(1)

# 2. 测试数据加载（单个文件）
print("\n===== 测试单个音频文件加载 =====")
try:
    import librosa
    # 找到第一个音频文件
    car_dir = os.path.join(DATA_PATH, 'wav', '0db', 'car')
    first_file = None
    for fname in os.listdir(car_dir):
        if fname.endswith('.wav'):
            first_file = os.path.join(car_dir, fname)
            break
    
    if first_file:
        print(f"加载文件: {first_file}")
        y, sr = librosa.load(first_file, sr=SAMPLE_RATE)
        print(f"音频长度: {len(y)}, 采样率: {sr}")
        print("✅ 单个音频文件加载正常")
    else:
        print("❌ 未找到音频文件")
        exit(1)
except Exception as e:
    print(f"❌ 音频文件加载异常: {e}")
    exit(1)

# 3. 测试数据生成器（极简版）
print("\n===== 测试数据生成器（1个文件） =====")
try:
    gen = UyghurDataGenerator(mode='train', max_files=1)
    print(f"数据集大小: {len(gen)}")
    
    batch_gen = gen.generate_batch()
    print("正在生成第一个批次...")
    start_time = time.time()
    batch = next(batch_gen)
    end_time = time.time()
    
    print(f"批次生成耗时: {end_time - start_time:.2f}秒")
    print(f"输入形状: {[x.shape for x in batch[0]]}")
    print(f"输出形状: {batch[1].shape}")
    print("✅ 数据生成器正常")
except Exception as e:
    print(f"❌ 数据生成器异常: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# 4. 测试模型构建（极简版）
print("\n===== 测试模型构建 =====")
try:
    print("正在构建模型...")
    start_time = time.time()
    model = build_robust_asr_model()
    end_time = time.time()
    
    print(f"模型构建耗时: {end_time - start_time:.2f}秒")
    print(f"模型参数数量: {model.count_params()}")
    print("✅ 模型构建正常")
except Exception as e:
    print(f"❌ 模型构建异常: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# 5. 测试模型编译
print("\n===== 测试模型编译 =====")
try:
    print("正在编译模型...")
    start_time = time.time()
    
    model.compile(
        optimizer='adam',
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    end_time = time.time()
    print(f"模型编译耗时: {end_time - start_time:.2f}秒")
    print("✅ 模型编译正常")
except Exception as e:
    print(f"❌ 模型编译异常: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# 6. 测试单步训练
print("\n===== 测试单步训练 =====")
try:
    print("正在执行单步训练...")
    X, y = batch
    
    start_time = time.time()
    loss_before = model.evaluate(X, y, verbose=0)
    print(f"训练前损失: {loss_before}")
    
    # 执行一步训练
    history = model.fit(X, y, epochs=1, verbose=1, batch_size=len(X[0]))
    
    loss_after = model.evaluate(X, y, verbose=0)
    print(f"训练后损失: {loss_after}")
    
    end_time = time.time()
    print(f"单步训练耗时: {end_time - start_time:.2f}秒")
    print("✅ 单步训练正常")
    
except Exception as e:
    print(f"❌ 单步训练异常: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

print("\n🎉 所有测试通过！训练环境配置正确。")
print("问题可能在于:")
print("1. 数据量太大导致训练很慢")
print("2. 批次大小设置不合适")
print("3. 模型太复杂")
print("\n建议:")
print("1. 使用更少的数据文件")
print("2. 减小批次大小")
print("3. 简化模型结构")
