import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

import tensorflow as tf
import psutil
import threading
import time
from data_loader import UyghurDataGenerator
from model import build_robust_asr_model
from config import *

class MemoryMonitor:
    def __init__(self):
        self.monitoring = False
        self.max_memory = 0
        self.start_memory = 0
        
    def start_monitoring(self):
        """开始监控内存"""
        self.monitoring = True
        self.start_memory = psutil.virtual_memory().percent
        self.max_memory = self.start_memory
        
        def monitor():
            while self.monitoring:
                current_memory = psutil.virtual_memory().percent
                cpu_percent = psutil.cpu_percent(interval=1)
                
                if current_memory > self.max_memory:
                    self.max_memory = current_memory
                
                print(f"[监控] CPU: {cpu_percent:5.1f}% | 内存: {current_memory:5.1f}% | 峰值: {self.max_memory:5.1f}%")
                time.sleep(5)
        
        self.monitor_thread = threading.Thread(target=monitor, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        end_memory = psutil.virtual_memory().percent
        print(f"\n内存使用总结:")
        print(f"  开始时: {self.start_memory:.1f}%")
        print(f"  结束时: {end_memory:.1f}%")
        print(f"  峰值: {self.max_memory:.1f}%")
        print(f"  增加: {end_memory - self.start_memory:.1f}%")

def check_memory_before_training():
    """训练前检查内存状态"""
    memory = psutil.virtual_memory()
    print(f"训练前内存状态:")
    print(f"  总内存: {memory.total / (1024**3):.1f} GB")
    print(f"  已使用: {memory.used / (1024**3):.1f} GB ({memory.percent:.1f}%)")
    print(f"  可用: {memory.available / (1024**3):.1f} GB")
    
    if memory.percent > 80:
        print("⚠️  警告: 内存使用率已经很高，建议:")
        print("   1. 关闭其他程序")
        print("   2. 减小批次大小")
        print("   3. 减少文件数量")
        return False
    return True

def train_with_memory_optimization():
    """带内存优化的训练"""
    print("===== 内存优化版训练 =====")
    
    # 检查训练前内存
    if not check_memory_before_training():
        choice = input("是否继续训练? (y/n): ")
        if choice.lower() != 'y':
            return
    
    # 启动内存监控
    monitor = MemoryMonitor()
    monitor.start_monitoring()
    
    try:
        # 配置TensorFlow内存增长
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
        
        # 清理会话
        tf.keras.backend.clear_session()
        
        # 使用较小的配置
        NUM_FILES = 5  # 进一步减少文件数
        BATCH_SIZE = 4  # 减小批次大小
        EPOCHS = 3
        
        print(f"\n内存优化配置:")
        print(f"  文件数: {NUM_FILES}")
        print(f"  批次大小: {BATCH_SIZE}")
        print(f"  训练轮数: {EPOCHS}")
        
        # 数据准备
        print("\n===== 准备数据 =====")
        train_gen = UyghurDataGenerator(
            mode='train', 
            max_files=NUM_FILES, 
            batch_size=BATCH_SIZE
        ).generate_batch()
        
        # 测试数据加载
        print("测试数据加载...")
        sample_batch = next(train_gen)
        print(f"批次形状: {[x.shape for x in sample_batch[0]]}")
        
        # 构建模型
        print("\n===== 构建模型 =====")
        model = build_robust_asr_model()
        print(f"模型参数: {model.count_params():,}")
        
        # 编译模型
        print("\n===== 编译模型 =====")
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.01),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        # 训练配置
        steps_per_epoch = max(1, NUM_FILES // BATCH_SIZE)
        validation_steps = 1
        
        print(f"\n===== 开始训练 =====")
        print(f"每epoch步数: {steps_per_epoch}")
        
        # 重新创建生成器
        train_gen = UyghurDataGenerator(
            mode='train', 
            max_files=NUM_FILES, 
            batch_size=BATCH_SIZE
        ).generate_batch()
        
        val_gen = UyghurDataGenerator(
            mode='train', 
            max_files=NUM_FILES, 
            batch_size=BATCH_SIZE
        ).generate_batch()
        
        # 开始训练
        start_time = time.time()
        
        history = model.fit(
            train_gen,
            steps_per_epoch=steps_per_epoch,
            validation_data=val_gen,
            validation_steps=validation_steps,
            epochs=EPOCHS,
            verbose=1,
            workers=1,
            use_multiprocessing=False
        )
        
        end_time = time.time()
        
        print(f"\n===== 训练完成 =====")
        print(f"训练时间: {end_time - start_time:.1f}秒")
        print(f"最终损失: {history.history['loss'][-1]:.4f}")
        print(f"最终准确率: {history.history['accuracy'][-1]:.4f}")
        
        # 保存模型
        model.save('memory_optimized_model.h5')
        print("模型已保存")
        
        # 测试预测
        print("\n===== 测试预测 =====")
        test_batch = next(val_gen)
        predictions = model.predict(test_batch[0], verbose=0)
        print(f"预测完成，输出形状: {predictions.shape}")
        
    except Exception as e:
        print(f"❌ 训练出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 停止监控
        monitor.stop_monitoring()
        
        # 清理内存
        tf.keras.backend.clear_session()
        print("\n已清理TensorFlow会话")

def check_system_resources():
    """检查系统资源"""
    print("===== 系统资源检查 =====")
    
    # CPU信息
    print(f"CPU核心数: {psutil.cpu_count()}")
    print(f"CPU使用率: {psutil.cpu_percent(interval=1):.1f}%")
    
    # 内存信息
    memory = psutil.virtual_memory()
    print(f"总内存: {memory.total / (1024**3):.1f} GB")
    print(f"可用内存: {memory.available / (1024**3):.1f} GB")
    print(f"内存使用率: {memory.percent:.1f}%")
    
    # 磁盘信息
    disk = psutil.disk_usage('.')
    print(f"磁盘可用空间: {disk.free / (1024**3):.1f} GB")
    
    # 建议
    if memory.percent > 70:
        print("\n⚠️  内存使用率较高，建议:")
        print("   - 关闭不必要的程序")
        print("   - 使用更小的批次大小")
    
    if memory.available / (1024**3) < 2:
        print("\n⚠️  可用内存不足2GB，建议:")
        print("   - 减少训练数据量")
        print("   - 使用批次大小4或更小")

if __name__ == "__main__":
    print("选择运行模式:")
    print("1. 检查系统资源")
    print("2. 内存优化训练")
    print("3. 两者都运行")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == "1":
        check_system_resources()
    elif choice == "2":
        train_with_memory_optimization()
    else:
        check_system_resources()
        print("\n" + "="*50)
        train_with_memory_optimization()
