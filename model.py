from tensorflow.keras.models import Model
from tensorflow.keras.layers import *
from config import *


def build_robust_asr_model():
    # 输入层
    noisy_input = Input(shape=(AUDIO_LENGTH, 1), name='noisy_input')
    env_input = Input(shape=(len(ENV_TYPES),), name='env_input')

    # 环境条件注入
    env_embed = Dense(64)(env_input)
    env_embed = Reshape((1, 64))(env_embed)

    # 主干网络（减少下采样次数）
    x = Conv1D(64, 50, strides=2, padding='same')(noisy_input)  # 修改strides从4到2
    x = BatchNormalization()(x)
    x = Activation('relu')(x)

    # 调整环境嵌入形状
    time_dim = x.shape[1]  # 现在应该是24000
    env_embed = UpSampling1D(time_dim)(env_embed)

    # 合并特征
    x = Concatenate()([x, env_embed])

    # 减少网络深度
    x = Conv1D(128, 25, padding='same')(x)
    x = BatchNormalization()(x)
    x = Activation('relu')(x)
    x = MaxPooling1D(4)(x)  # 最终时间维度变为6000

    # 输出层（确保与标签形状匹配）
    outputs = Dense(NUM_PHONEMES, activation='softmax')(x)

    return Model(inputs=[noisy_input, env_input], outputs=outputs)

if __name__ == "__main__":
    model = build_robust_asr_model()
    model.summary()