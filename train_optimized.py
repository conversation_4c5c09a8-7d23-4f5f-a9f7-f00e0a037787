import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

import tensorflow as tf
from data_loader import UyghurDataGenerator
from model import build_robust_asr_model
from config import *
import time

print("===== 优化版维吾尔语噪声鲁棒性训练 =====")

# 1. 初始化设置
print("\n===== 初始化训练环境 =====")
tf.keras.backend.clear_session()

# 2. 使用少量数据进行训练
NUM_FILES = 10  # 只使用10个音频文件
MINI_BATCH_SIZE = 8  # 减小批次大小
MINI_EPOCHS = 5  # 减少epoch数

print(f"使用 {NUM_FILES} 个音频文件进行训练")
print(f"批次大小: {MINI_BATCH_SIZE}")
print(f"训练轮数: {MINI_EPOCHS}")

# 3. 数据准备
print("\n===== 准备数据集 =====")
train_gen = UyghurDataGenerator(mode='train', max_files=NUM_FILES, batch_size=MINI_BATCH_SIZE).generate_batch()

# 测试数据加载
sample_batch = next(train_gen)
print("[验证] 批量数据形状:")
print(f" - 带噪语音: {sample_batch[0][0].shape}")
print(f" - 环境标签: {sample_batch[0][1].shape}")
print(f" - 音素标签: {sample_batch[1].shape}")

# 5. 构建模型
print("\n===== 构建模型 =====")
model = build_robust_asr_model()
print(f"模型参数总数: {model.count_params()}")

# 6. 模型编译
print("\n===== 编译模型 =====")
optimizer = tf.keras.optimizers.Adam(
    learning_rate=0.01,  # 提高学习率
    clipnorm=1.0
)

model.compile(
    optimizer=optimizer,
    loss='sparse_categorical_crossentropy',
    metrics=['accuracy'],
    run_eagerly=False
)

# 7. 训练配置
steps_per_epoch = max(1, NUM_FILES // MINI_BATCH_SIZE)  # 确保至少1步
validation_steps = 1

print(f"\n===== 训练参数 =====")
print(f" - 每epoch步数: {steps_per_epoch}")
print(f" - 验证步数: {validation_steps}")
print(f" - 批量大小: {MINI_BATCH_SIZE}")
print(f" - 总epoch数: {MINI_EPOCHS}")

# 8. 回调函数
callbacks = [
    tf.keras.callbacks.ModelCheckpoint(
        'optimized_model.h5',
        monitor='loss',
        save_best_only=True,
        mode='min',
        verbose=1
    ),
    tf.keras.callbacks.EarlyStopping(
        monitor='loss',
        patience=3,
        restore_best_weights=True,
        verbose=1
    )
]

# 9. 开始训练
print("\n===== 开始优化训练 =====")
start_time = time.time()

try:
    # 重新创建数据生成器
    train_gen = UyghurDataGenerator(mode='train', max_files=NUM_FILES, batch_size=MINI_BATCH_SIZE).generate_batch()
    val_gen = UyghurDataGenerator(mode='train', max_files=NUM_FILES, batch_size=MINI_BATCH_SIZE).generate_batch()
    
    history = model.fit(
        train_gen,
        steps_per_epoch=steps_per_epoch,
        validation_data=val_gen,
        validation_steps=validation_steps,
        epochs=MINI_EPOCHS,
        callbacks=callbacks,
        verbose=1,
        workers=1,
        use_multiprocessing=False
    )
    
    end_time = time.time()
    training_time = end_time - start_time
    
    print(f"\n===== 训练完成 =====")
    print(f"总训练时间: {training_time:.2f}秒 ({training_time/60:.1f}分钟)")
    print(f"最终训练损失: {history.history['loss'][-1]:.4f}")
    print(f"最终训练准确率: {history.history['accuracy'][-1]:.4f}")
    
    if 'val_loss' in history.history:
        print(f"最终验证损失: {history.history['val_loss'][-1]:.4f}")
        print(f"最终验证准确率: {history.history['val_accuracy'][-1]:.4f}")
    
    # 10. 保存模型
    model.save('optimized_trained_model.h5')
    print("模型已保存为 optimized_trained_model.h5")
    
    # 11. 测试预测
    print("\n===== 测试预测 =====")
    test_gen = UyghurDataGenerator(mode='train', max_files=NUM_FILES, batch_size=MINI_BATCH_SIZE).generate_batch()
    test_batch = next(test_gen)
    predictions = model.predict(test_batch[0], verbose=0)
    print(f"预测输出形状: {predictions.shape}")
    print(f"预测值范围: [{predictions.min():.4f}, {predictions.max():.4f}]")
    
    print("\n🎉 优化训练成功完成！")
    print("现在您可以:")
    print("1. 增加NUM_FILES来使用更多数据")
    print("2. 增加MINI_BATCH_SIZE来提高训练效率")
    print("3. 增加MINI_EPOCHS来进行更长时间的训练")
    
except Exception as e:
    print(f"❌ 训练过程中出现错误: {e}")
    import traceback
    traceback.print_exc()

print("\n===== 脚本执行完成 =====")
