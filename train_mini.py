import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # 只显示错误信息

import tensorflow as tf
from data_loader import UyghurDataGenerator
from model import build_robust_asr_model
from config import *

print("===== 迷你版训练脚本（仅使用3个音频文件） =====")

# 1. 初始化设置
print("\n===== 初始化训练环境 =====")
tf.keras.backend.clear_session()

# 2. 数据准备 - 只使用3个音频文件
print("\n===== 准备数据集（仅3个文件） =====")
train_gen = UyghurDataGenerator(mode='train', max_files=3).generate_batch()

# 测试数据加载
sample_batch = next(train_gen)
print("[验证] 批量数据形状:")
print(f" - 带噪语音: {sample_batch[0][0].shape}")
print(f" - 环境标签: {sample_batch[0][1].shape}")
print(f" - 音素标签: {sample_batch[1].shape}")

# 3. 构建模型
print("\n===== 构建模型 =====")
model = build_robust_asr_model()
print(f"模型参数总数: {model.count_params()}")

# 4. 模型编译
print("\n===== 编译模型 =====")
optimizer = tf.keras.optimizers.Adam(
    learning_rate=0.01,  # 提高学习率加快训练
    clipnorm=1.0
)

model.compile(
    optimizer=optimizer,
    loss='sparse_categorical_crossentropy',
    metrics=['accuracy'],
    run_eagerly=False
)

# 5. 训练配置（极少的训练量）
steps_per_epoch = 2  # 每个epoch只训练2步
validation_steps = 1  # 验证1步
epochs = 3  # 3个epoch

print(f"\n===== 训练参数 =====")
print(f" - 每epoch步数: {steps_per_epoch}")
print(f" - 验证步数: {validation_steps}")
print(f" - 批量大小: {BATCH_SIZE}")
print(f" - 总epoch数: {epochs}")

# 6. 简化的回调函数
callbacks = [
    tf.keras.callbacks.ModelCheckpoint(
        'mini_model.h5',
        monitor='loss',
        save_best_only=True,
        mode='min',
        verbose=1
    )
]

# 7. 开始训练
print("\n===== 开始迷你训练 =====")
try:
    # 重新创建数据生成器
    train_gen = UyghurDataGenerator(mode='train', max_files=3).generate_batch()
    val_gen = UyghurDataGenerator(mode='train', max_files=3).generate_batch()
    
    print("开始训练...")
    history = model.fit(
        train_gen,
        steps_per_epoch=steps_per_epoch,
        validation_data=val_gen,
        validation_steps=validation_steps,
        epochs=epochs,
        callbacks=callbacks,
        verbose=1,
        workers=1,
        use_multiprocessing=False
    )
    
    print("\n===== 迷你训练完成 =====")
    print(f"最终训练损失: {history.history['loss'][-1]:.4f}")
    print(f"最终训练准确率: {history.history['accuracy'][-1]:.4f}")
    print(f"最终验证损失: {history.history['val_loss'][-1]:.4f}")
    print(f"最终验证准确率: {history.history['val_accuracy'][-1]:.4f}")
    
    # 8. 保存模型
    model.save('mini_trained_model.h5')
    print("模型已保存为 mini_trained_model.h5")
    
    # 9. 测试预测
    print("\n===== 测试预测 =====")
    test_gen = UyghurDataGenerator(mode='train', max_files=3).generate_batch()
    test_batch = next(test_gen)
    predictions = model.predict(test_batch[0], verbose=0)
    print(f"预测输出形状: {predictions.shape}")
    print(f"预测值范围: [{predictions.min():.4f}, {predictions.max():.4f}]")
    
    print("\n🎉 迷你训练成功完成！")
    print("现在可以尝试使用更多数据进行完整训练。")
    
except Exception as e:
    print(f"❌ 训练过程中出现错误: {e}")
    import traceback
    traceback.print_exc()

print("\n===== 脚本执行完成 =====")
