import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

import tensorflow as tf
from data_loader import UyghurDataGenerator
from model import build_robust_asr_model
from config import *
import time

print("===== 渐进式维吾尔语噪声鲁棒性训练 =====")

# 训练阶段配置
STAGES = [
    {"name": "阶段1-快速验证", "files": 10, "batch_size": 8, "epochs": 3},
    {"name": "阶段2-小规模训练", "files": 50, "batch_size": 16, "epochs": 10},
    {"name": "阶段3-中等规模训练", "files": 200, "batch_size": 32, "epochs": 20},
    {"name": "阶段4-完整训练", "files": None, "batch_size": 32, "epochs": 50},  # None表示使用所有文件
]

def train_stage(stage_config, model=None):
    """训练单个阶段"""
    print(f"\n{'='*50}")
    print(f"开始 {stage_config['name']}")
    print(f"文件数: {stage_config['files'] or '全部'}")
    print(f"批次大小: {stage_config['batch_size']}")
    print(f"训练轮数: {stage_config['epochs']}")
    print(f"{'='*50}")
    
    # 数据准备
    train_gen = UyghurDataGenerator(
        mode='train', 
        max_files=stage_config['files'], 
        batch_size=stage_config['batch_size']
    ).generate_batch()
    
    val_gen = UyghurDataGenerator(
        mode='train', 
        max_files=stage_config['files'], 
        batch_size=stage_config['batch_size']
    ).generate_batch()
    
    # 如果没有提供模型，创建新模型
    if model is None:
        print("创建新模型...")
        model = build_robust_asr_model()
        
        # 编译模型
        optimizer = tf.keras.optimizers.Adam(
            learning_rate=0.001,  # 使用较小的学习率
            clipnorm=1.0
        )
        
        model.compile(
            optimizer=optimizer,
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy'],
            run_eagerly=False
        )
    
    # 训练配置
    if stage_config['files']:
        steps_per_epoch = max(1, stage_config['files'] // stage_config['batch_size'])
    else:
        steps_per_epoch = 45  # 使用默认值
    
    validation_steps = max(1, steps_per_epoch // 4)
    
    print(f"每epoch步数: {steps_per_epoch}")
    print(f"验证步数: {validation_steps}")
    
    # 回调函数
    callbacks = [
        tf.keras.callbacks.ModelCheckpoint(
            f"model_{stage_config['name'].replace('-', '_')}.h5",
            monitor='val_loss',
            save_best_only=True,
            mode='min',
            verbose=1
        ),
        tf.keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=3,
            verbose=1,
            min_lr=1e-6
        ),
        tf.keras.callbacks.EarlyStopping(
            monitor='val_loss',
            patience=5,
            restore_best_weights=True,
            verbose=1
        )
    ]
    
    # 开始训练
    start_time = time.time()
    
    try:
        history = model.fit(
            train_gen,
            steps_per_epoch=steps_per_epoch,
            validation_data=val_gen,
            validation_steps=validation_steps,
            epochs=stage_config['epochs'],
            callbacks=callbacks,
            verbose=1,
            workers=1,
            use_multiprocessing=False
        )
        
        end_time = time.time()
        training_time = end_time - start_time
        
        print(f"\n{stage_config['name']} 完成!")
        print(f"训练时间: {training_time:.1f}秒 ({training_time/60:.1f}分钟)")
        print(f"最终训练损失: {history.history['loss'][-1]:.4f}")
        print(f"最终训练准确率: {history.history['accuracy'][-1]:.4f}")
        
        if 'val_loss' in history.history:
            print(f"最终验证损失: {history.history['val_loss'][-1]:.4f}")
            print(f"最终验证准确率: {history.history['val_accuracy'][-1]:.4f}")
        
        return model, history
        
    except Exception as e:
        print(f"❌ {stage_config['name']} 训练失败: {e}")
        return model, None

def main():
    """主训练流程"""
    print("选择训练模式:")
    print("1. 快速验证 (阶段1)")
    print("2. 渐进训练 (阶段1-2)")
    print("3. 完整训练 (阶段1-4)")
    print("4. 自定义训练")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    if choice == "1":
        stages_to_run = [STAGES[0]]
    elif choice == "2":
        stages_to_run = STAGES[:2]
    elif choice == "3":
        stages_to_run = STAGES
    elif choice == "4":
        # 自定义配置
        files = input("文件数量 (回车使用全部): ").strip()
        files = int(files) if files else None
        
        batch_size = input("批次大小 (默认16): ").strip()
        batch_size = int(batch_size) if batch_size else 16
        
        epochs = input("训练轮数 (默认10): ").strip()
        epochs = int(epochs) if epochs else 10
        
        stages_to_run = [{
            "name": "自定义训练",
            "files": files,
            "batch_size": batch_size,
            "epochs": epochs
        }]
    else:
        print("无效选择，使用快速验证模式")
        stages_to_run = [STAGES[0]]
    
    # 执行训练
    model = None
    total_start_time = time.time()
    
    for stage in stages_to_run:
        model, history = train_stage(stage, model)
        if history is None:
            print("训练中断")
            break
    
    total_end_time = time.time()
    total_time = total_end_time - total_start_time
    
    print(f"\n🎉 所有训练阶段完成!")
    print(f"总训练时间: {total_time:.1f}秒 ({total_time/60:.1f}分钟)")
    
    if model:
        # 保存最终模型
        model.save('final_progressive_model.h5')
        print("最终模型已保存为 final_progressive_model.h5")
        
        # 测试预测
        print("\n===== 最终测试 =====")
        test_gen = UyghurDataGenerator(mode='train', max_files=10, batch_size=8).generate_batch()
        test_batch = next(test_gen)
        predictions = model.predict(test_batch[0], verbose=0)
        print(f"预测输出形状: {predictions.shape}")
        print(f"预测值范围: [{predictions.min():.4f}, {predictions.max():.4f}]")

if __name__ == "__main__":
    main()
