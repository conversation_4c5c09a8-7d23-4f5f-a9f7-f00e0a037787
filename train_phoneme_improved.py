import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

import tensorflow as tf
import numpy as np
from data_loader import UyghurDataGenerator
from config import *
import time

print("===== 改进的音素识别任务 =====")

class ImprovedPhonemeDataGenerator(UyghurDataGenerator):
    """改进的音素识别数据生成器"""
    
    def __init__(self, mode='train', max_files=None, batch_size=None):
        super().__init__(mode, max_files, batch_size)
        # 为每个说话人创建固定的音素模式
        self.speaker_phoneme_patterns = self._create_speaker_patterns()
        print(f"为 {len(self.speaker_phoneme_patterns)} 个说话人创建了音素模式")
    
    def _create_speaker_patterns(self):
        """为每个说话人创建固定的音素模式"""
        patterns = {}
        
        for car_path, cafe_path, white_path in self.file_triplets:
            filename = os.path.basename(car_path)
            
            # 提取说话人ID (如 F1000, M1001)
            if '_' in filename:
                speaker_id = filename.split('_')[0]
            else:
                speaker_id = f"Speaker_{hash(filename) % 100:02d}"
            
            if speaker_id not in patterns:
                # 为每个说话人创建固定的音素模式
                # 使用说话人ID的哈希值作为种子
                seed = hash(speaker_id) % (2**31)
                np.random.seed(seed)
                
                # 创建说话人特有的音素偏好
                # 每个说话人倾向于使用某些音素
                preferred_phonemes = np.random.choice(NUM_PHONEMES, size=8, replace=False)
                phoneme_probs = np.zeros(NUM_PHONEMES)
                phoneme_probs[preferred_phonemes] = np.random.uniform(0.5, 1.0, 8)
                phoneme_probs = phoneme_probs / phoneme_probs.sum()
                
                patterns[speaker_id] = {
                    'preferred_phonemes': preferred_phonemes,
                    'phoneme_probs': phoneme_probs
                }
        
        return patterns
    
    def _generate_phoneme_sequence(self, speaker_id, sentence_id):
        """为特定说话人和句子生成音素序列"""
        if speaker_id not in self.speaker_phoneme_patterns:
            # 如果说话人不存在，使用默认模式
            return np.random.randint(0, NUM_PHONEMES, 6000)
        
        pattern = self.speaker_phoneme_patterns[speaker_id]
        
        # 基于句子ID创建变化
        sentence_seed = hash(f"{speaker_id}_{sentence_id}") % (2**31)
        np.random.seed(sentence_seed)
        
        # 生成音素序列，倾向于使用说话人偏好的音素
        sequence = np.random.choice(
            NUM_PHONEMES, 
            size=6000, 
            p=pattern['phoneme_probs']
        )
        
        return sequence
    
    def generate_batch(self):
        while True:
            indices = np.random.permutation(len(self.file_triplets))
            
            if len(self.file_triplets) < self.batch_size:
                repeated_indices = []
                while len(repeated_indices) < self.batch_size:
                    repeated_indices.extend(indices)
                indices = np.array(repeated_indices[:self.batch_size])
            
            for i in range(0, len(indices), self.batch_size):
                batch_indices = indices[i:i + self.batch_size]
                actual_batch_size = len(batch_indices)
                
                if actual_batch_size < self.batch_size:
                    continue

                X_noisy = []
                X_env = []
                y_phoneme = []

                for idx in batch_indices:
                    car_path, cafe_path, white_path = self.file_triplets[idx]
                    env = np.random.choice(['cafe', 'white'])
                    noisy_path = cafe_path if env == 'cafe' else white_path

                    try:
                        # 提取说话人和句子信息
                        filename = os.path.basename(car_path)
                        if '_' in filename:
                            speaker_id = filename.split('_')[0]
                            sentence_id = filename.split('_')[1].split('.')[0]
                        else:
                            speaker_id = f"Speaker_{hash(filename) % 100:02d}"
                            sentence_id = "001"
                        
                        audio_data = self._process_audio(noisy_path)
                        X_noisy.append(audio_data.reshape(-1, 1))
                        X_env.append(np.eye(len(ENV_TYPES))[ENV_TYPES.index(env)])

                        # 生成基于说话人的音素序列
                        phoneme_seq = self._generate_phoneme_sequence(speaker_id, sentence_id)
                        y_phoneme.append(phoneme_seq)
                        
                    except Exception as e:
                        print(f"处理音频文件 {noisy_path} 时出错: {e}")
                        continue

                if len(X_noisy) == self.batch_size:
                    yield [np.array(X_noisy), np.array(X_env)], np.array(y_phoneme)

def create_improved_phoneme_model():
    """创建改进的音素识别模型"""
    # 输入层
    noisy_input = tf.keras.layers.Input(shape=(AUDIO_LENGTH, 1), name='noisy_input')
    env_input = tf.keras.layers.Input(shape=(len(ENV_TYPES),), name='env_input')

    # 环境条件处理
    env_embed = tf.keras.layers.Dense(32, activation='relu')(env_input)
    env_embed = tf.keras.layers.Reshape((1, 32))(env_embed)

    # 音频特征提取（简化版）
    x = tf.keras.layers.Conv1D(64, 25, strides=2, padding='same', activation='relu')(noisy_input)
    x = tf.keras.layers.BatchNormalization()(x)
    x = tf.keras.layers.MaxPooling1D(4)(x)  # 48000 -> 6000

    # 环境信息融合
    env_embed = tf.keras.layers.UpSampling1D(x.shape[1])(env_embed)
    x = tf.keras.layers.Concatenate()([x, env_embed])

    # 进一步特征提取
    x = tf.keras.layers.Conv1D(128, 15, padding='same', activation='relu')(x)
    x = tf.keras.layers.BatchNormalization()(x)

    # 音素预测层
    outputs = tf.keras.layers.Dense(NUM_PHONEMES, activation='softmax')(x)

    model = tf.keras.models.Model(inputs=[noisy_input, env_input], outputs=outputs)
    return model

def train_improved_phoneme():
    """训练改进的音素识别任务"""
    print("\n===== 改进的音素识别任务 =====")
    print("改进点：")
    print("1. 每个说话人有固定的音素偏好")
    print("2. 相同说话人的不同句子有相似的音素模式")
    print("3. 标签具有一致性和可学习性")
    
    # 配置
    NUM_FILES = 20
    BATCH_SIZE = 8  # 减小批次大小，因为任务更复杂
    EPOCHS = 20
    
    print(f"\n配置: {NUM_FILES}文件, 批次{BATCH_SIZE}, {EPOCHS}轮")
    
    # 数据准备
    print("\n===== 准备改进的音素数据 =====")
    train_gen = ImprovedPhonemeDataGenerator(
        mode='train', 
        max_files=NUM_FILES, 
        batch_size=BATCH_SIZE
    ).generate_batch()
    
    val_gen = ImprovedPhonemeDataGenerator(
        mode='train', 
        max_files=NUM_FILES, 
        batch_size=BATCH_SIZE
    ).generate_batch()
    
    # 测试数据一致性
    print("\n===== 测试数据一致性 =====")
    sample_batch = next(train_gen)
    print(f"输入形状: {[x.shape for x in sample_batch[0]]}")
    print(f"音素标签形状: {sample_batch[1].shape}")
    
    # 检查同一说话人的音素分布
    phoneme_dist = np.bincount(sample_batch[1].flatten(), minlength=NUM_PHONEMES)
    top_phonemes = np.argsort(phoneme_dist)[-5:][::-1]
    print(f"最常用的5个音素: {top_phonemes}")
    print(f"使用频率: {phoneme_dist[top_phonemes]}")
    
    # 构建模型
    print("\n===== 构建改进的音素模型 =====")
    model = create_improved_phoneme_model()
    print(f"模型参数: {model.count_params():,}")
    
    # 编译模型
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    # 训练配置
    steps_per_epoch = max(2, NUM_FILES // BATCH_SIZE)
    validation_steps = 1
    
    print(f"\n===== 开始改进训练 =====")
    print(f"每epoch步数: {steps_per_epoch}")
    print(f"随机基线准确率: {1/NUM_PHONEMES:.1%} ({1/NUM_PHONEMES:.4f})")
    print(f"目标准确率: >10% (显著超过随机)")
    
    # 回调函数
    callbacks = [
        tf.keras.callbacks.ModelCheckpoint(
            'improved_phoneme_model.h5',
            monitor='val_accuracy',
            save_best_only=True,
            mode='max',
            verbose=1
        ),
        tf.keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=5,
            verbose=1
        ),
        tf.keras.callbacks.EarlyStopping(
            monitor='val_accuracy',
            patience=8,
            mode='max',
            restore_best_weights=True,
            verbose=1
        )
    ]
    
    # 重新创建生成器
    train_gen = ImprovedPhonemeDataGenerator(
        mode='train', 
        max_files=NUM_FILES, 
        batch_size=BATCH_SIZE
    ).generate_batch()
    
    val_gen = ImprovedPhonemeDataGenerator(
        mode='train', 
        max_files=NUM_FILES, 
        batch_size=BATCH_SIZE
    ).generate_batch()
    
    # 开始训练
    start_time = time.time()
    
    try:
        history = model.fit(
            train_gen,
            steps_per_epoch=steps_per_epoch,
            validation_data=val_gen,
            validation_steps=validation_steps,
            epochs=EPOCHS,
            callbacks=callbacks,
            verbose=1,
            workers=1,
            use_multiprocessing=False
        )
        
        end_time = time.time()
        
        print(f"\n===== 改进音素识别训练完成 =====")
        print(f"训练时间: {end_time - start_time:.1f}秒 ({(end_time - start_time)/60:.1f}分钟)")
        
        final_train_acc = history.history['accuracy'][-1]
        final_val_acc = history.history['val_accuracy'][-1]
        initial_acc = history.history['accuracy'][0]
        improvement = final_train_acc - initial_acc
        
        print(f"最终训练准确率: {final_train_acc:.4f} ({final_train_acc*100:.2f}%)")
        print(f"最终验证准确率: {final_val_acc:.4f} ({final_val_acc*100:.2f}%)")
        print(f"准确率提升: {improvement:.4f} ({improvement*100:.2f}%)")
        
        # 与随机基线比较
        random_baseline = 1.0 / NUM_PHONEMES
        print(f"随机基线: {random_baseline:.4f} ({random_baseline*100:.2f}%)")
        
        if final_val_acc > random_baseline * 5:
            print("🎉 优秀！准确率是随机基线的5倍以上")
        elif final_val_acc > random_baseline * 3:
            print("✅ 很好！准确率是随机基线的3倍以上")
        elif final_val_acc > random_baseline * 2:
            print("👍 不错！准确率是随机基线的2倍以上")
        elif final_val_acc > random_baseline * 1.5:
            print("📈 有进步！准确率超过随机基线")
        else:
            print("⚠️ 准确率仍接近随机水平")
        
        # 保存模型
        model.save('improved_phoneme_trained_model.h5')
        print("改进音素模型已保存")
        
        return model, history
        
    except Exception as e:
        print(f"❌ 训练出错: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    train_improved_phoneme()
