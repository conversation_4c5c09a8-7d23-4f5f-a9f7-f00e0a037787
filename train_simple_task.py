import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

import tensorflow as tf
import numpy as np
from data_loader import UyghurDataGenerator
from config import *
import time

print("===== 简化任务版本：环境噪声分类 =====")

class SimpleTaskDataGenerator(UyghurDataGenerator):
    """简化任务的数据生成器：预测环境类型而不是音素"""
    
    def generate_batch(self):
        while True:
            indices = np.random.permutation(len(self.file_triplets))
            
            # 如果数据量少于批次大小，重复数据来填充批次
            if len(self.file_triplets) < self.batch_size:
                repeated_indices = []
                while len(repeated_indices) < self.batch_size:
                    repeated_indices.extend(indices)
                indices = np.array(repeated_indices[:self.batch_size])
            
            for i in range(0, len(indices), self.batch_size):
                batch_indices = indices[i:i + self.batch_size]
                actual_batch_size = len(batch_indices)
                
                if actual_batch_size < self.batch_size:
                    continue

                X_audio = []
                y_env = []  # 环境标签：0=cafe, 1=white

                for idx in batch_indices:
                    car_path, cafe_path, white_path = self.file_triplets[idx]
                    
                    # 随机选择一个噪声环境
                    env_choice = np.random.choice([0, 1])  # 0=cafe, 1=white
                    if env_choice == 0:
                        noisy_path = cafe_path
                        env_label = 0  # cafe
                    else:
                        noisy_path = white_path
                        env_label = 1  # white

                    try:
                        audio_data = self._process_audio(noisy_path)
                        X_audio.append(audio_data.reshape(-1, 1))
                        y_env.append(env_label)
                        
                    except Exception as e:
                        print(f"处理音频文件 {noisy_path} 时出错: {e}")
                        continue

                if len(X_audio) == self.batch_size:
                    yield np.array(X_audio), np.array(y_env)

def create_simple_model():
    """创建简单的环境分类模型"""
    model = tf.keras.Sequential([
        # 输入层
        tf.keras.layers.Input(shape=(AUDIO_LENGTH, 1)),
        
        # 第一个卷积块
        tf.keras.layers.Conv1D(32, 50, strides=4, padding='same', activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.MaxPooling1D(4),  # 48000 -> 3000
        
        # 第二个卷积块
        tf.keras.layers.Conv1D(64, 25, padding='same', activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.MaxPooling1D(4),  # 3000 -> 750
        
        # 第三个卷积块
        tf.keras.layers.Conv1D(128, 15, padding='same', activation='relu'),
        tf.keras.layers.BatchNormalization(),
        tf.keras.layers.GlobalAveragePooling1D(),  # 全局平均池化
        
        # 分类层
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.Dropout(0.5),
        tf.keras.layers.Dense(2, activation='softmax')  # 2个环境类别
    ])
    
    return model

def train_simple_task():
    """训练简化的环境分类任务"""
    print("\n===== 环境噪声分类任务 =====")
    print("任务：区分cafe噪声 vs white噪声")
    print("这比音素识别简单得多，应该能看到明显的学习效果")
    
    # 配置
    NUM_FILES = 20  # 增加文件数
    BATCH_SIZE = 16  # 增加批次大小
    EPOCHS = 15
    
    print(f"\n配置: {NUM_FILES}文件, 批次{BATCH_SIZE}, {EPOCHS}轮")
    
    # 数据准备
    print("\n===== 准备数据 =====")
    train_gen = SimpleTaskDataGenerator(
        mode='train', 
        max_files=NUM_FILES, 
        batch_size=BATCH_SIZE
    ).generate_batch()
    
    val_gen = SimpleTaskDataGenerator(
        mode='train', 
        max_files=NUM_FILES, 
        batch_size=BATCH_SIZE
    ).generate_batch()
    
    # 测试数据
    sample_batch = next(train_gen)
    print(f"音频数据形状: {sample_batch[0].shape}")
    print(f"环境标签形状: {sample_batch[1].shape}")
    print(f"标签示例: {sample_batch[1][:8]}")  # 显示前8个标签
    
    # 检查标签分布
    unique, counts = np.unique(sample_batch[1], return_counts=True)
    print(f"标签分布: {dict(zip(unique, counts))}")
    
    # 构建简单模型
    print("\n===== 构建简单模型 =====")
    model = create_simple_model()
    model.summary()
    print(f"模型参数: {model.count_params():,}")
    
    # 编译模型
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    # 训练配置
    steps_per_epoch = max(2, NUM_FILES // BATCH_SIZE)
    validation_steps = 1
    
    print(f"\n===== 开始训练 =====")
    print(f"每epoch步数: {steps_per_epoch}")
    print(f"理论最高准确率: 100% (完美分类)")
    print(f"随机基线准确率: 50% (二分类)")
    
    # 回调函数
    callbacks = [
        tf.keras.callbacks.ModelCheckpoint(
            'simple_task_model.h5',
            monitor='val_accuracy',
            save_best_only=True,
            mode='max',
            verbose=1
        ),
        tf.keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=3,
            verbose=1,
            min_lr=1e-6
        ),
        tf.keras.callbacks.EarlyStopping(
            monitor='val_accuracy',
            patience=5,
            mode='max',
            restore_best_weights=True,
            verbose=1
        )
    ]
    
    # 重新创建生成器
    train_gen = SimpleTaskDataGenerator(
        mode='train', 
        max_files=NUM_FILES, 
        batch_size=BATCH_SIZE
    ).generate_batch()
    
    val_gen = SimpleTaskDataGenerator(
        mode='train', 
        max_files=NUM_FILES, 
        batch_size=BATCH_SIZE
    ).generate_batch()
    
    # 开始训练
    start_time = time.time()
    
    try:
        history = model.fit(
            train_gen,
            steps_per_epoch=steps_per_epoch,
            validation_data=val_gen,
            validation_steps=validation_steps,
            epochs=EPOCHS,
            callbacks=callbacks,
            verbose=1,
            workers=1,
            use_multiprocessing=False
        )
        
        end_time = time.time()
        
        print(f"\n===== 简化任务训练完成 =====")
        print(f"训练时间: {end_time - start_time:.1f}秒 ({(end_time - start_time)/60:.1f}分钟)")
        print(f"最终训练损失: {history.history['loss'][-1]:.4f}")
        print(f"最终训练准确率: {history.history['accuracy'][-1]:.4f}")
        print(f"最终验证损失: {history.history['val_loss'][-1]:.4f}")
        print(f"最终验证准确率: {history.history['val_accuracy'][-1]:.4f}")
        
        # 详细分析
        initial_acc = history.history['accuracy'][0]
        final_acc = history.history['accuracy'][-1]
        improvement = final_acc - initial_acc
        
        print(f"\n📊 详细分析:")
        print(f"  初始准确率: {initial_acc:.4f} ({initial_acc*100:.1f}%)")
        print(f"  最终准确率: {final_acc:.4f} ({final_acc*100:.1f}%)")
        print(f"  提升幅度: {improvement:.4f} ({improvement*100:.1f}%)")
        print(f"  随机基线: 0.5000 (50.0%)")
        
        if final_acc > 0.8:
            print("🎉 优秀！准确率超过80%")
        elif final_acc > 0.7:
            print("✅ 很好！准确率超过70%")
        elif final_acc > 0.6:
            print("👍 不错！准确率超过60%")
        elif final_acc > 0.5:
            print("📈 有进步！准确率超过随机水平")
        else:
            print("⚠️ 准确率仍接近随机水平，可能需要更多训练")
        
        # 验证准确率分析
        val_acc = history.history['val_accuracy'][-1]
        if abs(final_acc - val_acc) < 0.1:
            print("✅ 训练和验证准确率接近，模型泛化良好")
        else:
            print("⚠️ 训练和验证准确率差异较大，可能存在过拟合")
        
        # 保存模型
        model.save('simple_task_trained_model.h5')
        print("简化任务模型已保存")
        
        # 测试预测
        print("\n===== 测试预测 =====")
        test_batch = next(val_gen)
        predictions = model.predict(test_batch[0], verbose=0)
        predicted_classes = np.argmax(predictions, axis=1)
        true_classes = test_batch[1]
        
        print(f"预测类别: {predicted_classes[:8]}")
        print(f"真实类别: {true_classes[:8]}")
        print(f"预测概率范围: [{predictions.min():.3f}, {predictions.max():.3f}]")
        
        # 计算测试准确率
        test_accuracy = np.mean(predicted_classes == true_classes)
        print(f"测试批次准确率: {test_accuracy:.4f} ({test_accuracy*100:.1f}%)")
        
        return model, history
        
    except Exception as e:
        print(f"❌ 训练出错: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    train_simple_task()
