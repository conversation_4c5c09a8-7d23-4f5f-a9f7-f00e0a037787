import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['TF_FORCE_GPU_ALLOW_GROWTH'] = 'true'

import tensorflow as tf
from data_loader import UyghurDataGenerator
from model import build_robust_asr_model
from config import *

print("===== 最小内存占用训练版本 =====")

# 配置TensorFlow使用最少内存
tf.config.threading.set_intra_op_parallelism_threads(1)
tf.config.threading.set_inter_op_parallelism_threads(1)

# 清理会话
tf.keras.backend.clear_session()

# 极小的配置
NUM_FILES = 3
BATCH_SIZE = 2  # 最小批次
EPOCHS = 2

print(f"极简配置: {NUM_FILES}文件, 批次{BATCH_SIZE}, {EPOCHS}轮")

try:
    # 数据准备
    print("准备数据...")
    train_gen = UyghurDataGenerator(
        mode='train', 
        max_files=NUM_FILES, 
        batch_size=BATCH_SIZE
    ).generate_batch()
    
    # 构建最简模型
    print("构建模型...")
    model = build_robust_asr_model()
    
    # 编译
    model.compile(
        optimizer='adam',
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    # 训练
    print("开始训练...")
    history = model.fit(
        train_gen,
        steps_per_epoch=1,
        epochs=EPOCHS,
        verbose=1
    )
    
    print("✅ 训练完成!")
    print(f"最终损失: {history.history['loss'][-1]:.4f}")
    
except Exception as e:
    print(f"❌ 错误: {e}")

finally:
    # 强制清理
    tf.keras.backend.clear_session()
    print("已清理内存")
