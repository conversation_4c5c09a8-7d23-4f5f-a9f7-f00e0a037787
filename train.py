import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # 只显示错误信息
os.environ['TF_FORCE_GPU_ALLOW_GROWTH'] = 'true'  # 允许GPU内存增长

import tensorflow as tf
from data_loader import UyghurDataGenerator
from model import build_robust_asr_model
from config import *

# 1. 初始化设置
print("===== 初始化训练环境 =====")
tf.keras.backend.clear_session()

# 2. 数据准备
print("\n===== 准备数据集 =====")
train_gen = UyghurDataGenerator(mode='train').generate_batch()
val_gen = UyghurDataGenerator(mode='val').generate_batch()

# 测试数据加载
sample_batch = next(train_gen)
print("[验证] 批量数据形状:")
print(f" - 带噪语音: {sample_batch[0][0].shape}")
print(f" - 环境标签: {sample_batch[0][1].shape}")
print(f" - 音素标签: {sample_batch[1].shape}")

# 3. 构建模型
print("\n===== 构建模型 =====")
model = build_robust_asr_model()
model.summary()  # 打印模型结构

# 4. 模型编译
print("\n===== 编译模型 =====")
optimizer = tf.keras.optimizers.Adam(
    learning_rate=LEARNING_RATE,
    clipnorm=1.0  # 防止梯度爆炸
)

model.compile(
    optimizer=optimizer,
    loss='sparse_categorical_crossentropy',
    metrics=['accuracy'],
    run_eagerly=False
)

# 5. 训练配置
steps_per_epoch = len(UyghurDataGenerator(mode='train')) // BATCH_SIZE
validation_steps = len(UyghurDataGenerator(mode='val')) // BATCH_SIZE

print(f"\n===== 训练参数 =====")
print(f" - 每epoch步数: {steps_per_epoch}")
print(f" - 验证步数: {validation_steps}")
print(f" - 批量大小: {BATCH_SIZE}")
print(f" - 总epoch数: {EPOCHS}")

# 6. 回调函数
callbacks = [
    tf.keras.callbacks.ModelCheckpoint(
        'best_model.h5',
        monitor='val_accuracy',
        save_best_only=True,
        mode='max'
    ),
    tf.keras.callbacks.ReduceLROnPlateau(
        monitor='val_loss',
        factor=0.5,
        patience=3,
        verbose=1
    ),
    tf.keras.callbacks.EarlyStopping(
        monitor='val_loss',
        patience=5,
        restore_best_weights=True
    ),
    tf.keras.callbacks.TensorBoard(
        log_dir='./logs',
        histogram_freq=1,
        update_freq='epoch'
    )
]

# 7. 开始训练
print("\n===== 开始训练 =====")
history = model.fit(
    train_gen,
    steps_per_epoch=steps_per_epoch,
    validation_data=val_gen,
    validation_steps=validation_steps,
    epochs=EPOCHS,
    callbacks=callbacks,
    verbose=1,
    max_queue_size=10,
    workers=4,
    use_multiprocessing=True
)

# 8. 保存最终模型
print("\n===== 保存模型 =====")
model.save('final_model.h5')
print("训练完成！模型已保存为 final_model.h5")

# 9. 训练曲线可视化
import matplotlib.pyplot as plt

plt.figure(figsize=(12, 5))
plt.subplot(1, 2, 1)
plt.plot(history.history['accuracy'], label='训练准确率')
plt.plot(history.history['val_accuracy'], label='验证准确率')
plt.title('模型准确率')
plt.legend()

plt.subplot(1, 2, 2)
plt.plot(history.history['loss'], label='训练损失')
plt.plot(history.history['val_loss'], label='验证损失')
plt.title('模型损失')
plt.legend()

plt.savefig('training_history.png')
print("训练曲线已保存为 training_history.png")