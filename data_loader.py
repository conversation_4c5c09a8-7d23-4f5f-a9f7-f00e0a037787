import numpy as np
import librosa
from config import *


class UyghurDataGenerator:
    def __init__(self, mode='train', max_files=None):
        self.file_triplets = self._load_triplets(max_files)
        self.mode = mode

    def __len__(self):
        """返回数据集的总样本数"""
        return len(self.file_triplets)


    def _load_triplets(self, max_files=None):
        """加载严格对应的三种环境音频"""
        triplets = []
        car_dir = os.path.join(DATA_PATH, 'wav', '0db', 'car')
        cafe_dir = os.path.join(DATA_PATH, 'wav', '0db', 'cafe')
        white_dir = os.path.join(DATA_PATH, 'wav', '0db', 'white')

        file_list = sorted(os.listdir(car_dir))  # 排序确保一致性
        if max_files:
            file_list = file_list[:max_files]  # 只取前几个文件

        for fname in file_list:
            if fname.endswith('.wav'):
                car_path = os.path.join(car_dir, fname)
                cafe_path = os.path.join(cafe_dir, fname)
                white_path = os.path.join(white_dir, fname)

                # 确保三个文件都存在
                if os.path.exists(car_path) and os.path.exists(cafe_path) and os.path.exists(white_path):
                    triplets.append((car_path, cafe_path, white_path))
                else:
                    print(f"警告: 文件 {fname} 在某些环境中缺失")

        print(f"成功加载 {len(triplets)} 组完整的音频三元组")
        return triplets

    def _process_audio(self, path):
        """音频标准化处理"""
        y, _ = librosa.load(path, sr=SAMPLE_RATE)
        y = y[:AUDIO_LENGTH] if len(y) > AUDIO_LENGTH else np.pad(y, (0, AUDIO_LENGTH - len(y)))
        return (y - np.mean(y)) / np.std(y)

    def generate_batch(self):
        while True:
            indices = np.random.permutation(len(self.file_triplets))
            for i in range(0, len(indices), BATCH_SIZE):
                batch_indices = indices[i:i + BATCH_SIZE]
                actual_batch_size = len(batch_indices)

                # 如果批次大小不足，跳过这个批次
                if actual_batch_size < BATCH_SIZE:
                    continue

                # 调整标签长度以匹配模型输出
                X_noisy = []
                X_env = []
                y_phoneme = []

                for idx in batch_indices:
                    car_path, cafe_path, white_path = self.file_triplets[idx]
                    env = np.random.choice(['cafe', 'white'])
                    noisy_path = cafe_path if env == 'cafe' else white_path

                    try:
                        audio_data = self._process_audio(noisy_path)
                        X_noisy.append(audio_data.reshape(-1, 1))  # 确保形状正确
                        X_env.append(np.eye(len(ENV_TYPES))[ENV_TYPES.index(env)])

                        # 生成6000长度的标签以匹配模型输出
                        y_phoneme.append(np.random.randint(0, NUM_PHONEMES, 6000))  # 替换为真实标签
                    except Exception as e:
                        print(f"处理音频文件 {noisy_path} 时出错: {e}")
                        continue

                if len(X_noisy) == BATCH_SIZE:
                    yield [np.array(X_noisy), np.array(X_env)], np.array(y_phoneme)


# 测试数据加载
if __name__ == "__main__":
    gen = UyghurDataGenerator()
    print(f"成功加载 {len(gen.file_triplets)} 组数据")
    (X_noisy, X_env), y = next(gen.generate_batch())
    print("输入带噪语音形状:", X_noisy.shape)
    print("环境标签形状:", X_env.shape)
    print("音素标签形状:", y.shape)