import numpy as np
import librosa
from config import *


class UyghurDataGenerator:
    def __init__(self, mode='train'):
        self.file_triplets = self._load_triplets()
        self.mode = mode

    def __len__(self):
        """返回数据集的总样本数"""
        return len(self.file_triplets)


    def _load_triplets(self):
        """加载严格对应的三种环境音频"""
        triplets = []
        for fname in os.listdir(os.path.join(DATA_PATH, 'wav', '0db', 'car')):
            if fname.endswith('.wav'):
                triplets.append((
                    os.path.join(DATA_PATH, 'wav', '0db', 'car', fname),  # 纯净语音
                    os.path.join(DATA_PATH, 'wav', '0db', 'cafe', fname),  # 餐厅噪声
                    os.path.join(DATA_PATH, 'wav', '0db', 'white', fname)  # 收银机噪声
                ))
        return triplets

    def _process_audio(self, path):
        """音频标准化处理"""
        y, _ = librosa.load(path, sr=SAMPLE_RATE)
        y = y[:AUDIO_LENGTH] if len(y) > AUDIO_LENGTH else np.pad(y, (0, AUDIO_LENGTH - len(y)))
        return (y - np.mean(y)) / np.std(y)

    def generate_batch(self):
        while True:
            indices = np.random.permutation(len(self.file_triplets))
            for i in range(0, len(indices), BATCH_SIZE):
                batch = self.file_triplets[i:i + BATCH_SIZE]

                # 调整标签长度以匹配模型输出
                X_noisy = []
                X_env = []
                y_phoneme = []

                for car_path, cafe_path, white_path in batch:
                    env = np.random.choice(['cafe', 'white'])
                    noisy_path = cafe_path if env == 'cafe' else white_path

                    X_noisy.append(self._process_audio(noisy_path))
                    X_env.append(np.eye(len(ENV_TYPES))[ENV_TYPES.index(env)])

                    # 生成6000长度的标签以匹配模型输出
                    y_phoneme.append(np.random.randint(0, NUM_PHONEMES, 6000))  # 替换为真实标签

                yield [np.array(X_noisy), np.array(X_env)], np.array(y_phoneme)


# 测试数据加载
if __name__ == "__main__":
    gen = UyghurDataGenerator()
    print(f"成功加载 {len(gen.file_triplets)} 组数据")
    (X_noisy, X_env), y = next(gen.generate_batch())
    print("输入带噪语音形状:", X_noisy.shape)
    print("环境标签形状:", X_env.shape)
    print("音素标签形状:", y.shape)