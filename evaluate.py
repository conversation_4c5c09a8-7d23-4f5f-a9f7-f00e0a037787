import matplotlib.pyplot as plt
import numpy as np
from model import build_robust_asr_model
from data_loader import UyghurDataGenerator


def evaluate_model(model_path='best_model.h5'):
    # 加载模型
    model = tf.keras.models.load_model(model_path)

    # 测试集评估
    test_gen = UyghurDataGenerator(mode='test').generate_batch()
    results = model.evaluate(test_gen, steps=100)
    print(f"测试集损失: {results[0]:.4f}, 准确率: {results[1]:.4f}")

    # 可视化预测样例
    (X_noisy, X_env), y_true = next(test_gen)
    y_pred = model.predict([X_noisy[:3], X_env[:3]])

    plt.figure(figsize=(12, 6))
    for i in range(3):
        plt.subplot(3, 1, i + 1)
        plt.plot(X_noisy[i], alpha=0.5, label='带噪语音')
        plt.plot(y_pred[i].argmax(axis=-1), label='预测音素')
        plt.legend()
    plt.savefig('prediction_samples.png')


if __name__ == "__main__":
    evaluate_model()