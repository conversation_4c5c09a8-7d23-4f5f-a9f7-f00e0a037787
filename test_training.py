import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # 只显示错误信息

import tensorflow as tf
from data_loader import UyghurDataGenerator
from model import build_robust_asr_model
from config import *

print("===== 测试训练环境 =====")

# 1. 测试数据加载
print("\n===== 测试数据加载 =====")
try:
    train_gen = UyghurDataGenerator(mode='train').generate_batch()
    sample_batch = next(train_gen)
    print("[成功] 数据加载正常")
    print(f" - 带噪语音形状: {sample_batch[0][0].shape}")
    print(f" - 环境标签形状: {sample_batch[0][1].shape}")
    print(f" - 音素标签形状: {sample_batch[1].shape}")
except Exception as e:
    print(f"[错误] 数据加载失败: {e}")
    exit(1)

# 2. 测试模型构建
print("\n===== 测试模型构建 =====")
try:
    model = build_robust_asr_model()
    print("[成功] 模型构建正常")
    print(f"模型参数总数: {model.count_params()}")
except Exception as e:
    print(f"[错误] 模型构建失败: {e}")
    exit(1)

# 3. 测试模型编译
print("\n===== 测试模型编译 =====")
try:
    optimizer = tf.keras.optimizers.Adam(learning_rate=LEARNING_RATE, clipnorm=1.0)
    model.compile(
        optimizer=optimizer,
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy'],
        run_eagerly=False
    )
    print("[成功] 模型编译正常")
except Exception as e:
    print(f"[错误] 模型编译失败: {e}")
    exit(1)

# 4. 测试单步训练
print("\n===== 测试单步训练 =====")
try:
    # 获取一个批次的数据
    X, y = sample_batch
    
    # 执行一步训练
    loss = model.train_on_batch(X, y)
    print(f"[成功] 单步训练完成，损失值: {loss}")
    
    # 测试预测
    predictions = model.predict(X, verbose=0)
    print(f"[成功] 预测完成，输出形状: {predictions.shape}")
    
except Exception as e:
    print(f"[错误] 单步训练失败: {e}")
    exit(1)

# 5. 测试短期训练
print("\n===== 测试短期训练 =====")
try:
    # 创建新的数据生成器
    train_gen = UyghurDataGenerator(mode='train').generate_batch()
    
    # 训练2步
    history = model.fit(
        train_gen,
        steps_per_epoch=2,
        epochs=1,
        verbose=1,
        workers=1,
        use_multiprocessing=False
    )
    
    print("[成功] 短期训练完成")
    print(f"训练损失: {history.history['loss']}")
    print(f"训练准确率: {history.history['accuracy']}")
    
except Exception as e:
    print(f"[错误] 短期训练失败: {e}")
    exit(1)

print("\n===== 所有测试通过！ =====")
print("您的训练环境配置正确，可以开始完整训练。")
